{"$schema": "https://getcomposer.org/schema.json", "name": "s-a-c/workos-sac", "type": "project", "description": "The official Laravel starter kit for Livewire.", "keywords": ["laravel", "framework"], "license": "MIT", "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "laravel-comments": {"type": "composer", "url": "https://satis.spatie.be"}}, "require": {"php": "^8.4", "ext-pdo": "*", "aliziodev/laravel-taxonomy": "^2.4", "filament/filament": "^4.0", "glhd/bits": "^0.6.1", "laravel/folio": "^1.1", "laravel/framework": "^12.0", "laravel/tinker": "^2.10", "laravel/workos": "^0.1", "livewire/flux": "^2.2", "livewire/flux-pro": "^2.2", "livewire/volt": "^1.7", "nnjeim/world": "^1.1", "nunomaduro/essentials": "*", "nunomaduro/laravel-optimize-database": "^1.0", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-comments": "^2.3", "spatie/laravel-comments-livewire": "^3.2", "spatie/laravel-data": "^4.17", "spatie/laravel-fractal": "^6.3", "spatie/laravel-permission": "^6.20", "spatie/laravel-query-builder": "^6.3", "spatie/laravel-sluggable": "^3.7", "staudenmeir/laravel-adjacency-list": "^1.25", "wildside/userstamps": "^3.1"}, "require-dev": {"alebatistella/duskapiconf": "^1.2", "barryvdh/laravel-debugbar": "^3.15", "blackfire/php-sdk": "^2.5", "driftingly/rector-laravel": "^2.0", "ergebnis/composer-normalize": "^2.47", "fakerphp/faker": "^1.24", "filament/upgrade": "^4.0", "friendsofphp/php-cs-fixer": "^3.75", "infection/infection": "^0.29", "jasonmccreary/laravel-test-assertions": "^2.8", "larastan/larastan": "^3.5", "laravel-shift/blueprint": "^2.12", "laravel/dusk": "^8.3", "laravel/pail": "^1.2", "laravel/pint": "^1.22", "laravel/sail": "^1.43", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.8", "nunomaduro/phpinsights": "^2.13", "peckphp/peck": "^0.1", "pestphp/pest": "^3.8", "pestphp/pest-plugin": "^3.x-dev", "pestphp/pest-plugin-arch": "^3.1", "pestphp/pest-plugin-faker": "^3.0", "pestphp/pest-plugin-laravel": "^3.2", "pestphp/pest-plugin-livewire": "^3.0", "pestphp/pest-plugin-stressless": "^3.1", "pestphp/pest-plugin-type-coverage": "^3.5", "pestphp/pest-plugin-watch": "^3.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpbench/phpbench": "^1.4", "phpstan/phpstan-deprecation-rules": "^2.0", "rector/rector": "^2.0", "rector/type-perfect": "^2.0", "roave/security-advisories": "dev-latest", "soloterm/solo": "^0.5", "spatie/laravel-blade-comments": "^1.4", "spatie/laravel-horizon-watcher": "^1.1", "spatie/laravel-ray": "^1.40", "spatie/laravel-web-tinker": "^1.10", "spatie/pest-plugin-snapshots": "^2.2", "symfony/polyfill-php84": "^1.31"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "exclude-from-classmap": ["vendor/league/flysystem/src/Local/", "vendor/laravel/pint/app/Providers/"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "pnpm concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"pnpm run dev\" --names=server,queue,logs,vite --kill-others"], "analyze": ["@pint:test", "@phpstan", "@rector:dry-run", "@insights"], "clear": ["@php artisan cache:clear --ansi", "@php artisan config:clear --ansi", "@php artisan route:clear --ansi", "@php artisan view:clear --ansi", "@php artisan clear-compiled"], "fix": ["@pint --parallel", "@rector"], "insights": "phpinsights", "phpstan": "phpstan analyse", "pint": "pint", "pint:test": "pint --test", "rector": "rector process", "rector:dry-run": "rector process --dry-run", "test": ["@clear", "@php artisan test"], "test:api": "pest --group=api", "test:arch": "pest --group=arch", "test:coverage": "pest --coverage", "test:coverage-html": "pest --coverage --coverage-html=reports/coverage", "test:database": "pest --group=database", "test:error-handling": "pest --group=error-handling", "test:feature": "pest --group=feature", "test:integration": "pest --group=integration", "test:parallel": "pest --parallel", "test:performance": "pest --group=performance", "test:security": "pest --group=security", "test:stress": "pest --group=stress", "test:type-coverage": "pest --type-coverage", "test:ui": "pest --group=ui", "test:unit": "pest --group=unit", "test:validation": "pest --group=validation"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "ergebnis/composer-normalize": true, "infection/extension-installer": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}