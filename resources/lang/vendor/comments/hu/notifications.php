<?php

return [
    'approve_comment' => 'Hozzászólás jóváhagyása',
    'reject_comment' => 'Hozzászó<PERSON>ás elutasítása',
    'view_comment' => 'Hozzászólás megtekintése',

    'pending_comment_mail_subject' => 'Egy új hozzászólás jóváhagyásra vár',
    'pending_comment_mail_body' => ':commentator_name hozzászólása jóváhagyásra vár ":commentable_name" tém<PERSON>ban.',

    'approved_comment_mail_subject' => 'Új hozzászólás érkezett',
    'approved_comment_mail_title' => 'Új hozzászólás érkezett a(z) ":commentable_name" témáho<PERSON>',
    'approved_comment_mail_body' => 'A hozzászólást írta: :commentator_name',

    'enum_description_participating'=> 'Ha részt veszek benne',
    'enum_description_all' => 'Minden hozzászóláskor',
    'enum_description_none' => 'Soha',
    'enum_longdescription_participating' => '<PERSON><PERSON><PERSON>, ha részt veszek a beszélgetésben',
    'enum_longdescription_all' => 'Ka<PERSON>jak értesítőt minden hozzászólás esetén',
    'enum_longdescription_none' => 'Ne kapjak értesítőt egyik esetben sem',
];
