<?php

return [
    'approve_comment' => 'Approve comment',
    'reject_comment' => 'Reject comment',
    'view_comment' => 'View comment',

    'pending_comment_mail_subject' => 'A new comment is awaiting approval',
    'pending_comment_mail_body' => 'A pending comment :commentable_name by :commentator_name awaits your approval',

    'approved_comment_mail_subject' => 'A new comment was posted',
    'approved_comment_mail_title' => 'A new comment on ":commentable_name"',
    'approved_comment_mail_body' => 'Posted by :commentator_name',

    'enum_description_participating'=> 'When participating',
    'enum_description_all' => 'On all comments',
    'enum_description_none' => 'Never',
    'enum_longdescription_participating' => 'Get notified when participating',
    'enum_longdescription_all' => 'Get notified on all comments',
    'enum_longdescription_none' => 'Never be notified',
];
